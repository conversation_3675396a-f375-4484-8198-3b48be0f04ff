/**
 * InputHandler - 六子棋输入处理模块
 * 负责处理用户输入事件，包括鼠标点击和键盘事件
 */
class InputHandler {
    constructor(canvas, renderer) {
        this.canvas = canvas;
        this.renderer = renderer;
        this.clickCallback = null;
        this.isEnabled = true;
        
        this.init();
    }

    /**
     * 初始化事件监听器
     */
    init() {
        // 绑定鼠标点击事件
        this.canvas.addEventListener('click', (event) => {
            this.handleClick(event);
        });
        
        // 绑定鼠标移动事件（用于悬停效果）
        this.canvas.addEventListener('mousemove', (event) => {
            this.handleMouseMove(event);
        });
        
        // 绑定鼠标离开事件
        this.canvas.addEventListener('mouseleave', () => {
            this.handleMouseLeave();
        });
        
        // 设置鼠标样式
        this.canvas.style.cursor = 'pointer';
    }

    /**
     * 处理鼠标点击事件
     * @param {MouseEvent} event - 鼠标事件对象
     */
    handleClick(event) {
        // 如果输入被禁用，直接返回
        if (!this.isEnabled) {
            return;
        }
        
        // 阻止默认行为
        event.preventDefault();
        
        // 获取鼠标点击位置
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        // 转换为棋盘坐标
        const boardPos = this.renderer.screenToBoard(event.clientX, event.clientY);
        
        if (boardPos) {
            // 调用回调函数
            if (this.clickCallback) {
                this.clickCallback(boardPos.row, boardPos.col);
            }
        }
    }

    /**
     * 处理鼠标移动事件
     * @param {MouseEvent} event - 鼠标事件对象
     */
    handleMouseMove(event) {
        if (!this.isEnabled) {
            return;
        }
        
        // 获取鼠标位置对应的棋盘坐标
        const boardPos = this.renderer.screenToBoard(event.clientX, event.clientY);
        
        if (boardPos) {
            // 在有效位置上，显示手型光标
            this.canvas.style.cursor = 'pointer';
        } else {
            // 在无效位置上，显示默认光标
            this.canvas.style.cursor = 'default';
        }
    }

    /**
     * 处理鼠标离开事件
     */
    handleMouseLeave() {
        // 恢复默认光标
        this.canvas.style.cursor = 'default';
    }

    /**
     * 设置点击回调函数
     * @param {Function} callback - 回调函数，参数为 (row, col)
     */
    setClickCallback(callback) {
        this.clickCallback = callback;
    }

    /**
     * 启用输入处理
     */
    enable() {
        this.isEnabled = true;
        this.canvas.style.cursor = 'pointer';
    }

    /**
     * 禁用输入处理
     */
    disable() {
        this.isEnabled = false;
        this.canvas.style.cursor = 'not-allowed';
    }

    /**
     * 检查输入是否启用
     * @returns {boolean} 是否启用
     */
    isInputEnabled() {
        return this.isEnabled;
    }

    /**
     * 添加键盘事件监听（用于快捷键）
     */
    addKeyboardListeners() {
        document.addEventListener('keydown', (event) => {
            this.handleKeyDown(event);
        });
    }

    /**
     * 处理键盘按下事件
     * @param {KeyboardEvent} event - 键盘事件对象
     */
    handleKeyDown(event) {
        if (!this.isEnabled) {
            return;
        }
        
        switch (event.key) {
            case 'r':
            case 'R':
                // R键重新开始游戏
                if (this.restartCallback) {
                    this.restartCallback();
                }
                break;
            case 'Escape':
                // ESC键可以用于其他功能
                break;
        }
    }

    /**
     * 设置重新开始回调函数
     * @param {Function} callback - 重新开始回调函数
     */
    setRestartCallback(callback) {
        this.restartCallback = callback;
    }

    /**
     * 获取鼠标相对于Canvas的坐标
     * @param {MouseEvent} event - 鼠标事件对象
     * @returns {Object} 坐标对象 {x, y}
     */
    getMousePosition(event) {
        const rect = this.canvas.getBoundingClientRect();
        return {
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
        };
    }

    /**
     * 销毁输入处理器，移除所有事件监听器
     */
    destroy() {
        // 移除Canvas事件监听器
        this.canvas.removeEventListener('click', this.handleClick);
        this.canvas.removeEventListener('mousemove', this.handleMouseMove);
        this.canvas.removeEventListener('mouseleave', this.handleMouseLeave);
        
        // 清空回调函数
        this.clickCallback = null;
        this.restartCallback = null;
        
        // 重置状态
        this.isEnabled = false;
    }
}
