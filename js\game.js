/**
 * GameController - 六子棋主控制器
 * 负责协调所有模块，控制游戏流程
 */
class GameController {
    constructor() {
        // 游戏状态
        this.gameState = 'playing'; // 'playing', 'blackWin', 'whiteWin', 'draw'
        this.currentPlayer = 1; // 1=黑子, 2=白子
        this.lastMove = null; // 最后一步棋的位置
        this.moveHistory = []; // 历史步骤记录
        
        // 游戏模块
        this.gameBoard = null;
        this.renderer = null;
        this.inputHandler = null;
        this.winChecker = null;
        
        // DOM元素
        this.canvas = null;
        this.currentPlayerText = null;
        this.gameMessage = null;
        this.restartBtn = null;
        this.undoBtn = null;
        
        this.init();
    }

    /**
     * 初始化游戏
     */
    init() {
        // 获取DOM元素
        this.canvas = document.getElementById('game-canvas');
        this.currentPlayerText = document.getElementById('current-player-text');
        this.gameMessage = document.getElementById('game-message');
        this.restartBtn = document.getElementById('restart-btn');
        this.undoBtn = document.getElementById('undo-btn');
        
        if (!this.canvas) {
            console.error('找不到游戏画布元素');
            return;
        }
        
        // 初始化游戏模块
        this.gameBoard = new GameBoard();
        this.renderer = new Renderer(this.canvas);
        this.inputHandler = new InputHandler(this.canvas, this.renderer);
        this.winChecker = new WinChecker();
        
        // 设置事件监听器
        this.setupEventListeners();
        
        // 开始新游戏
        this.startNewGame();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 设置点击回调
        this.inputHandler.setClickCallback((row, col) => {
            this.handleMove(row, col);
        });
        
        // 设置重新开始按钮
        if (this.restartBtn) {
            this.restartBtn.addEventListener('click', () => {
                this.startNewGame();
            });
        }

        // 设置悔棋按钮
        if (this.undoBtn) {
            this.undoBtn.addEventListener('click', () => {
                this.undoMove();
            });
        }
        
        // 添加键盘监听器
        this.inputHandler.addKeyboardListeners();
        this.inputHandler.setRestartCallback(() => {
            this.startNewGame();
        });
    }

    /**
     * 开始新游戏
     */
    startNewGame() {
        // 重置游戏状态
        this.gameState = 'playing';
        this.currentPlayer = 1; // 黑子先手
        this.lastMove = null;
        this.moveHistory = []; // 清空历史记录
        
        // 重置棋盘
        this.gameBoard.reset();
        
        // 启用输入
        this.inputHandler.enable();
        
        // 更新UI
        this.updateUI();
        
        // 渲染游戏画面
        this.render();
        
        console.log('新游戏开始');
    }

    /**
     * 处理玩家落子
     * @param {number} row - 行坐标
     * @param {number} col - 列坐标
     */
    handleMove(row, col) {
        // 检查游戏是否还在进行中
        if (this.gameState !== 'playing') {
            return;
        }
        
        // 尝试放置棋子
        if (this.gameBoard.placePiece(row, col, this.currentPlayer)) {
            // 记录历史步骤
            this.moveHistory.push({
                row: row,
                col: col,
                player: this.currentPlayer
            });

            // 记录最后一步
            this.lastMove = { row, col };
            
            // 检查胜负
            const winResult = this.winChecker.checkWin(this.gameBoard, row, col, this.currentPlayer);
            
            if (winResult) {
                // 有玩家获胜
                this.gameState = this.currentPlayer === 1 ? 'blackWin' : 'whiteWin';
                this.inputHandler.disable();
                console.log(`玩家${this.currentPlayer}获胜！`);
            } else if (this.winChecker.checkDraw(this.gameBoard)) {
                // 平局
                this.gameState = 'draw';
                this.inputHandler.disable();
                console.log('游戏平局！');
            } else {
                // 继续游戏，切换玩家
                this.switchPlayer();
            }
            
            // 更新UI和渲染
            this.updateUI();
            this.render();
        } else {
            // 无效落子
            console.log('无效的落子位置');
        }
    }

    /**
     * 切换当前玩家
     */
    switchPlayer() {
        this.currentPlayer = this.currentPlayer === 1 ? 2 : 1;
    }

    /**
     * 悔棋功能
     */
    undoMove() {
        // 检查是否有步骤可以悔棋
        if (this.moveHistory.length === 0) {
            console.log('没有可以悔棋的步骤');
            return;
        }

        // 检查游戏是否已结束
        if (this.gameState !== 'playing') {
            // 如果游戏已结束，悔棋后重新开始游戏
            this.gameState = 'playing';
            this.inputHandler.enable();
        }

        // 获取最后一步
        const lastMove = this.moveHistory.pop();

        // 从棋盘上移除最后一步的棋子
        this.gameBoard.board[lastMove.row][lastMove.col] = 0;

        // 更新最后一步记录
        if (this.moveHistory.length > 0) {
            const prevMove = this.moveHistory[this.moveHistory.length - 1];
            this.lastMove = { row: prevMove.row, col: prevMove.col };
        } else {
            this.lastMove = null;
        }

        // 切换回上一个玩家
        this.currentPlayer = lastMove.player;

        // 更新UI和渲染
        this.updateUI();
        this.render();

        console.log(`悔棋成功，撤销了${lastMove.player === 1 ? '黑子' : '白子'}在(${lastMove.row}, ${lastMove.col})的落子`);
    }

    /**
     * 渲染游戏画面
     */
    render() {
        // 渲染棋盘和棋子
        this.renderer.render(this.gameBoard);
        
        // 如果有最后一步，标记出来
        if (this.lastMove) {
            this.renderer.drawLastMoveMarker(this.lastMove.row, this.lastMove.col);
        }
    }

    /**
     * 更新用户界面
     */
    updateUI() {
        // 更新当前玩家显示
        if (this.currentPlayerText) {
            this.currentPlayerText.textContent = this.currentPlayer === 1 ? '黑子' : '白子';
        }
        
        // 更新游戏消息
        if (this.gameMessage) {
            let message = '';
            let className = '';
            
            switch (this.gameState) {
                case 'playing':
                    message = `轮到${this.currentPlayer === 1 ? '黑子' : '白子'}下棋`;
                    className = '';
                    break;
                case 'blackWin':
                    message = '🎉 黑子获胜！';
                    className = 'win';
                    break;
                case 'whiteWin':
                    message = '🎉 白子获胜！';
                    className = 'win';
                    break;
                case 'draw':
                    message = '🤝 游戏平局！';
                    className = 'error';
                    break;
            }
            
            this.gameMessage.textContent = message;
            this.gameMessage.className = 'game-message ' + className;
        }

        // 更新悔棋按钮状态
        if (this.undoBtn) {
            this.undoBtn.disabled = this.moveHistory.length === 0;
        }
    }

    /**
     * 获取当前游戏状态
     * @returns {string} 游戏状态
     */
    getGameState() {
        return this.gameState;
    }

    /**
     * 获取当前玩家
     * @returns {number} 当前玩家
     */
    getCurrentPlayer() {
        return this.currentPlayer;
    }

    /**
     * 获取游戏棋盘
     * @returns {GameBoard} 游戏棋盘对象
     */
    getGameBoard() {
        return this.gameBoard;
    }

    /**
     * 销毁游戏控制器
     */
    destroy() {
        if (this.inputHandler) {
            this.inputHandler.destroy();
        }
        
        // 清空引用
        this.gameBoard = null;
        this.renderer = null;
        this.inputHandler = null;
        this.winChecker = null;
    }
}

// 当页面加载完成后启动游戏
document.addEventListener('DOMContentLoaded', () => {
    // 创建游戏实例
    window.game = new GameController();
    
    console.log('六子棋游戏已启动');
});
