<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>六子棋游戏</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="game-container">
        <header class="game-header">
            <h1>六子棋游戏</h1>
            <div class="game-info">
                <div class="current-player">
                    <span>当前玩家：</span>
                    <span id="current-player-text">黑子</span>
                </div>
                <button id="restart-btn" class="restart-btn">重新开始</button>
                <button id="undo-btn" class="undo-btn">悔棋</button>
            </div>
        </header>
        
        <main class="game-main">
            <div class="game-board-container">
                <canvas id="game-canvas" width="600" height="600"></canvas>
            </div>
            
            <div class="game-status">
                <div id="game-message" class="game-message">游戏开始，黑子先手</div>
            </div>
        </main>
        
        <footer class="game-footer">
            <p>规则：在15×15棋盘上连成6个子获胜</p>
        </footer>
    </div>

    <!-- JavaScript模块 -->
    <script src="js/board.js"></script>
    <script src="js/renderer.js"></script>
    <script src="js/input.js"></script>
    <script src="js/checker.js"></script>
    <script src="js/game.js"></script>
</body>
</html>
