/* 六子棋游戏样式 - 赛博朋克风格 */

/* 导入赛博朋克字体 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Rajdhani', 'Microsoft YaHei', Arial, sans-serif;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0a0a0a 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
    padding: 20px 10px;
}

/* 赛博朋克背景动画网格 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: grid-move 20s linear infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* 游戏容器 - 赛博朋克风格 */
.game-container {
    background:
        linear-gradient(145deg, rgba(10, 10, 10, 0.9) 0%, rgba(26, 26, 46, 0.9) 50%, rgba(15, 52, 96, 0.9) 100%);
    border: 2px solid transparent;
    border-radius: 20px;
    background-clip: padding-box;
    box-shadow:
        0 0 50px rgba(0, 255, 255, 0.3),
        0 0 100px rgba(255, 0, 255, 0.2),
        inset 0 0 50px rgba(0, 255, 255, 0.1);
    padding: 20px;
    max-width: 750px;
    width: 100%;
    max-height: 95vh;
    position: relative;
    backdrop-filter: blur(10px);
    margin: 10px auto;
    overflow-y: auto;
}

/* 容器发光边框效果 */
.game-container::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ffff);
    border-radius: 22px;
    z-index: -1;
    animation: border-glow 3s ease-in-out infinite alternate;
}

@keyframes border-glow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 游戏头部 - 赛博朋克风格 */
.game-header {
    text-align: center;
    margin-bottom: 30px;
}

.game-header h1 {
    font-family: 'Orbitron', monospace;
    color: #00ffff;
    font-size: 3em;
    font-weight: 900;
    margin-bottom: 20px;
    text-shadow:
        0 0 10px #00ffff,
        0 0 20px #00ffff,
        0 0 30px #00ffff,
        0 0 40px #00ffff;
    letter-spacing: 3px;
    animation: title-glow 2s ease-in-out infinite alternate;
}

@keyframes title-glow {
    0% {
        text-shadow:
            0 0 10px #00ffff,
            0 0 20px #00ffff,
            0 0 30px #00ffff,
            0 0 40px #00ffff;
    }
    100% {
        text-shadow:
            0 0 20px #00ffff,
            0 0 30px #00ffff,
            0 0 40px #00ffff,
            0 0 50px #00ffff,
            0 0 60px #ff00ff;
    }
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background:
        linear-gradient(145deg, rgba(0, 255, 255, 0.1) 0%, rgba(255, 0, 255, 0.1) 100%);
    padding: 15px 25px;
    border-radius: 15px;
    border: 1px solid rgba(0, 255, 255, 0.3);
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.2),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
    backdrop-filter: blur(5px);
}

.current-player {
    font-family: 'Rajdhani', monospace;
    font-size: 1.4em;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

#current-player-text {
    color: #00ffff;
    text-shadow:
        0 0 10px #00ffff,
        0 0 20px #00ffff;
    animation: player-pulse 1.5s ease-in-out infinite alternate;
}

@keyframes player-pulse {
    0% { opacity: 0.8; }
    100% { opacity: 1; }
}

/* 赛博朋克按钮样式 */
.restart-btn, .undo-btn {
    font-family: 'Rajdhani', monospace;
    background: linear-gradient(145deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 255, 255, 0.1) 100%);
    color: #00ffff;
    border: 2px solid #00ffff;
    padding: 12px 24px;
    border-radius: 10px;
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.3),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
}

.undo-btn {
    background: linear-gradient(145deg, rgba(255, 255, 0, 0.2) 0%, rgba(255, 255, 0, 0.1) 100%);
    color: #ffff00;
    border-color: #ffff00;
    box-shadow:
        0 0 20px rgba(255, 255, 0, 0.3),
        inset 0 0 20px rgba(255, 255, 0, 0.1);
}

.restart-btn:hover {
    background: linear-gradient(145deg, rgba(0, 255, 255, 0.4) 0%, rgba(0, 255, 255, 0.2) 100%);
    box-shadow:
        0 0 30px rgba(0, 255, 255, 0.6),
        inset 0 0 30px rgba(0, 255, 255, 0.2);
    transform: translateY(-2px);
}

.undo-btn:hover {
    background: linear-gradient(145deg, rgba(255, 255, 0, 0.4) 0%, rgba(255, 255, 0, 0.2) 100%);
    box-shadow:
        0 0 30px rgba(255, 255, 0, 0.6),
        inset 0 0 30px rgba(255, 255, 0, 0.2);
    transform: translateY(-2px);
}

.restart-btn:active, .undo-btn:active {
    transform: translateY(0);
}

.undo-btn:disabled {
    background: linear-gradient(145deg, rgba(108, 117, 125, 0.2) 0%, rgba(108, 117, 125, 0.1) 100%);
    color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.5;
    box-shadow: none;
}

/* 按钮发光动画 */
.restart-btn::before, .undo-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.restart-btn:hover::before, .undo-btn:hover::before {
    left: 100%;
}

/* 游戏主体 - 赛博朋克风格 */
.game-main {
    text-align: center;
}

.game-board-container {
    display: inline-block;
    border: 3px solid transparent;
    border-radius: 15px;
    background:
        linear-gradient(145deg, rgba(0, 0, 0, 0.8) 0%, rgba(26, 26, 46, 0.8) 100%);
    padding: 20px;
    margin-bottom: 30px;
    position: relative;
    box-shadow:
        0 0 50px rgba(0, 255, 255, 0.4),
        0 0 100px rgba(255, 0, 255, 0.2),
        inset 0 0 50px rgba(0, 255, 255, 0.1);
}

/* 棋盘容器发光边框 */
.game-board-container::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ffff);
    border-radius: 18px;
    z-index: -1;
    animation: board-border-glow 4s ease-in-out infinite;
}

@keyframes board-border-glow {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

#game-canvas {
    display: block;
    cursor: pointer;
    border-radius: 10px;
    box-shadow:
        0 0 30px rgba(0, 255, 255, 0.3),
        inset 0 0 30px rgba(0, 255, 255, 0.1);
}

/* 游戏状态 - 赛博朋克风格 */
.game-status {
    margin-bottom: 30px;
}

.game-message {
    font-family: 'Rajdhani', monospace;
    font-size: 1.5em;
    font-weight: 600;
    color: #ffffff;
    background:
        linear-gradient(145deg, rgba(0, 255, 255, 0.1) 0%, rgba(255, 0, 255, 0.1) 100%);
    padding: 20px;
    border-radius: 15px;
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-left: 5px solid #00ffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    box-shadow:
        0 0 30px rgba(0, 255, 255, 0.2),
        inset 0 0 30px rgba(0, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    animation: message-pulse 2s ease-in-out infinite alternate;
}

@keyframes message-pulse {
    0% { opacity: 0.9; }
    100% { opacity: 1; }
}

.game-message.win {
    background:
        linear-gradient(145deg, rgba(0, 255, 0, 0.2) 0%, rgba(0, 255, 255, 0.2) 100%);
    color: #00ff00;
    border-left-color: #00ff00;
    text-shadow:
        0 0 10px #00ff00,
        0 0 20px #00ff00;
    box-shadow:
        0 0 40px rgba(0, 255, 0, 0.4),
        inset 0 0 40px rgba(0, 255, 0, 0.1);
    animation: win-celebration 1s ease-in-out infinite alternate;
}

@keyframes win-celebration {
    0% {
        transform: scale(1);
        text-shadow:
            0 0 10px #00ff00,
            0 0 20px #00ff00;
    }
    100% {
        transform: scale(1.02);
        text-shadow:
            0 0 20px #00ff00,
            0 0 30px #00ff00,
            0 0 40px #00ff00;
    }
}

.game-message.error {
    background:
        linear-gradient(145deg, rgba(255, 0, 0, 0.2) 0%, rgba(255, 0, 255, 0.2) 100%);
    color: #ff0066;
    border-left-color: #ff0066;
    text-shadow:
        0 0 10px #ff0066,
        0 0 20px #ff0066;
    box-shadow:
        0 0 40px rgba(255, 0, 102, 0.4),
        inset 0 0 40px rgba(255, 0, 102, 0.1);
}

/* 游戏底部 - 赛博朋克风格 */
.game-footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    font-family: 'Rajdhani', monospace;
    font-size: 1em;
    font-weight: 400;
    border-top: 1px solid rgba(0, 255, 255, 0.3);
    padding-top: 20px;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 10px 5px;
    }

    .game-container {
        margin: 5px;
        padding: 15px;
        max-height: 98vh;
    }

    .game-header h1 {
        font-size: 2em;
        margin-bottom: 15px;
    }

    .game-info {
        flex-direction: column;
        gap: 10px;
        padding: 10px 15px;
    }

    .restart-btn, .undo-btn {
        margin-left: 0;
        margin-top: 5px;
        padding: 10px 20px;
        font-size: 1em;
    }

    #game-canvas {
        max-width: 100%;
        height: auto;
    }

    .game-board-container {
        padding: 15px;
        margin-bottom: 20px;
    }

    .game-message {
        font-size: 1.2em;
        padding: 15px;
    }
}

@media (max-height: 700px) {
    .game-container {
        max-height: 95vh;
        overflow-y: auto;
    }

    .game-header h1 {
        font-size: 2.2em;
        margin-bottom: 15px;
    }

    .game-board-container {
        margin-bottom: 15px;
    }

    .game-status {
        margin-bottom: 15px;
    }
}

/* 自定义滚动条样式 */
.game-container::-webkit-scrollbar {
    width: 8px;
}

.game-container::-webkit-scrollbar-track {
    background: rgba(0, 255, 255, 0.1);
    border-radius: 4px;
}

.game-container::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.game-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #ff00ff, #ffff00);
    box-shadow: 0 0 15px rgba(255, 0, 255, 0.7);
}

/* 赛博朋克棋子动画效果 */
@keyframes piece-place {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 0;
        filter: blur(10px);
    }
    50% {
        transform: scale(1.3) rotate(180deg);
        opacity: 0.8;
        filter: blur(2px);
    }
    100% {
        transform: scale(1) rotate(360deg);
        opacity: 1;
        filter: blur(0px);
    }
}

.piece-animation {
    animation: piece-place 0.5s ease-out;
}

/* 赛博朋克粒子效果 */
@keyframes particle-float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.8;
    }
}

/* 额外的赛博朋克装饰元素 */
.game-container::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, #00ffff 0%, transparent 70%);
    border-radius: 50%;
    animation: particle-float 3s ease-in-out infinite;
}
