/**
 * GameBoard - 六子棋棋盘模块
 * 负责管理棋盘状态和棋子放置逻辑
 */
class GameBoard {
    constructor() {
        // 棋盘大小：15x15
        this.size = 15;
        // 棋盘状态：0=空位，1=黑子，2=白子
        this.board = [];
        this.init();
    }

    /**
     * 初始化棋盘
     */
    init() {
        this.board = [];
        for (let i = 0; i < this.size; i++) {
            this.board[i] = [];
            for (let j = 0; j < this.size; j++) {
                this.board[i][j] = 0; // 0表示空位
            }
        }
    }

    /**
     * 检查指定位置是否为空
     * @param {number} row - 行坐标 (0-14)
     * @param {number} col - 列坐标 (0-14)
     * @returns {boolean} 是否为空位
     */
    isEmpty(row, col) {
        // 检查坐标是否有效
        if (row < 0 || row >= this.size || col < 0 || col >= this.size) {
            return false;
        }
        return this.board[row][col] === 0;
    }

    /**
     * 在指定位置放置棋子
     * @param {number} row - 行坐标 (0-14)
     * @param {number} col - 列坐标 (0-14)
     * @param {number} player - 玩家 (1=黑子, 2=白子)
     * @returns {boolean} 是否成功放置
     */
    placePiece(row, col, player) {
        // 检查位置是否有效且为空
        if (!this.isEmpty(row, col)) {
            return false;
        }
        
        // 检查玩家参数是否有效
        if (player !== 1 && player !== 2) {
            return false;
        }

        // 放置棋子
        this.board[row][col] = player;
        return true;
    }

    /**
     * 获取指定位置的棋子
     * @param {number} row - 行坐标
     * @param {number} col - 列坐标
     * @returns {number} 棋子类型 (0=空位, 1=黑子, 2=白子)
     */
    getPiece(row, col) {
        if (row < 0 || row >= this.size || col < 0 || col >= this.size) {
            return -1; // 无效位置
        }
        return this.board[row][col];
    }

    /**
     * 重置棋盘
     */
    reset() {
        this.init();
    }

    /**
     * 获取棋盘的副本（用于AI或其他分析）
     * @returns {Array} 棋盘状态的副本
     */
    getBoardCopy() {
        return this.board.map(row => [...row]);
    }

    /**
     * 检查棋盘是否已满
     * @returns {boolean} 棋盘是否已满
     */
    isFull() {
        for (let i = 0; i < this.size; i++) {
            for (let j = 0; j < this.size; j++) {
                if (this.board[i][j] === 0) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 获取棋盘大小
     * @returns {number} 棋盘大小
     */
    getSize() {
        return this.size;
    }

    /**
     * 打印棋盘状态（调试用）
     */
    printBoard() {
        console.log('棋盘状态:');
        for (let i = 0; i < this.size; i++) {
            let row = '';
            for (let j = 0; j < this.size; j++) {
                const piece = this.board[i][j];
                if (piece === 0) row += '· ';
                else if (piece === 1) row += '● ';
                else if (piece === 2) row += '○ ';
            }
            console.log(row);
        }
    }
}
