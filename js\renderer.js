/**
 * Renderer - 六子棋渲染模块
 * 负责Canvas绘制操作，包括棋盘和棋子的渲染
 */
class Renderer {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.boardSize = 15;
        this.cellSize = 40; // 每个格子的大小
        this.pieceRadius = 16; // 棋子半径
        this.boardMargin = 20; // 棋盘边距
        
        // 设置Canvas实际尺寸
        this.canvasSize = this.cellSize * (this.boardSize - 1) + this.boardMargin * 2;
        this.canvas.width = this.canvasSize;
        this.canvas.height = this.canvasSize;
        
        // 设置高DPI支持
        this.setupHighDPI();
    }

    /**
     * 设置高DPI显示支持
     */
    setupHighDPI() {
        const devicePixelRatio = window.devicePixelRatio || 1;
        const rect = this.canvas.getBoundingClientRect();
        
        this.canvas.width = rect.width * devicePixelRatio;
        this.canvas.height = rect.height * devicePixelRatio;
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        this.ctx.scale(devicePixelRatio, devicePixelRatio);
    }

    /**
     * 清空画布
     */
    clear() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }

    /**
     * 绘制棋盘网格
     */
    drawBoard() {
        const ctx = this.ctx;
        
        // 设置线条样式
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 1;
        
        // 绘制赛博朋克背景
        const gradient = ctx.createRadialGradient(
            this.canvasSize / 2, this.canvasSize / 2, 0,
            this.canvasSize / 2, this.canvasSize / 2, this.canvasSize / 2
        );
        gradient.addColorStop(0, 'rgba(0, 20, 40, 0.9)');
        gradient.addColorStop(0.5, 'rgba(0, 40, 80, 0.8)');
        gradient.addColorStop(1, 'rgba(0, 10, 20, 0.9)');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, this.canvasSize, this.canvasSize);

        // 绘制发光网格线
        ctx.shadowColor = '#00ffff';
        ctx.shadowBlur = 3;

        for (let i = 0; i < this.boardSize; i++) {
            const pos = this.boardMargin + i * this.cellSize;

            // 绘制垂直线
            ctx.beginPath();
            ctx.moveTo(pos, this.boardMargin);
            ctx.lineTo(pos, this.canvasSize - this.boardMargin);
            ctx.strokeStyle = i % 2 === 0 ? 'rgba(0, 255, 255, 0.6)' : 'rgba(0, 255, 255, 0.3)';
            ctx.lineWidth = i === 0 || i === this.boardSize - 1 ? 2 : 1;
            ctx.stroke();

            // 绘制水平线
            ctx.beginPath();
            ctx.moveTo(this.boardMargin, pos);
            ctx.lineTo(this.canvasSize - this.boardMargin, pos);
            ctx.strokeStyle = i % 2 === 0 ? 'rgba(0, 255, 255, 0.6)' : 'rgba(0, 255, 255, 0.3)';
            ctx.lineWidth = i === 0 || i === this.boardSize - 1 ? 2 : 1;
            ctx.stroke();
        }

        // 重置阴影
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        
        // 绘制特殊点位（天元等）
        this.drawSpecialPoints();
    }

    /**
     * 绘制棋盘上的特殊点位 - 赛博朋克风格
     */
    drawSpecialPoints() {
        const ctx = this.ctx;
        const points = [
            [3, 3], [3, 11], [11, 3], [11, 11], // 四个角的星位
            [7, 7] // 天元
        ];

        points.forEach(([row, col]) => {
            const x = this.boardMargin + col * this.cellSize;
            const y = this.boardMargin + row * this.cellSize;

            // 绘制发光的特殊点位
            ctx.shadowColor = '#ff00ff';
            ctx.shadowBlur = 8;

            // 外圈
            ctx.beginPath();
            ctx.arc(x, y, 5, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(255, 0, 255, 0.6)';
            ctx.fill();

            // 内圈
            ctx.beginPath();
            ctx.arc(x, y, 2, 0, Math.PI * 2);
            ctx.fillStyle = '#ffffff';
            ctx.fill();

            // 重置阴影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
        });
    }

    /**
     * 绘制棋子
     * @param {number} row - 行坐标
     * @param {number} col - 列坐标
     * @param {number} player - 玩家 (1=黑子, 2=白子)
     */
    drawPiece(row, col, player) {
        const ctx = this.ctx;
        const x = this.boardMargin + col * this.cellSize;
        const y = this.boardMargin + row * this.cellSize;
        
        // 绘制赛博朋克风格棋子
        if (player === 1) {
            // 黑子 - 赛博朋克蓝色
            // 外发光效果
            ctx.shadowColor = '#00ffff';
            ctx.shadowBlur = 15;

            // 绘制外圈发光
            ctx.beginPath();
            ctx.arc(x, y, this.pieceRadius + 3, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(0, 255, 255, 0.3)';
            ctx.fill();

            // 绘制主体
            ctx.beginPath();
            ctx.arc(x, y, this.pieceRadius, 0, Math.PI * 2);
            const gradient = ctx.createRadialGradient(x - 5, y - 5, 0, x, y, this.pieceRadius);
            gradient.addColorStop(0, '#00ffff');
            gradient.addColorStop(0.3, '#0088ff');
            gradient.addColorStop(0.7, '#0044aa');
            gradient.addColorStop(1, '#001144');
            ctx.fillStyle = gradient;
            ctx.fill();

            // 绘制边框
            ctx.strokeStyle = '#00ffff';
            ctx.lineWidth = 2;
            ctx.stroke();

        } else {
            // 白子 - 赛博朋克粉色
            // 外发光效果
            ctx.shadowColor = '#ff00ff';
            ctx.shadowBlur = 15;

            // 绘制外圈发光
            ctx.beginPath();
            ctx.arc(x, y, this.pieceRadius + 3, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(255, 0, 255, 0.3)';
            ctx.fill();

            // 绘制主体
            ctx.beginPath();
            ctx.arc(x, y, this.pieceRadius, 0, Math.PI * 2);
            const gradient = ctx.createRadialGradient(x - 5, y - 5, 0, x, y, this.pieceRadius);
            gradient.addColorStop(0, '#ffffff');
            gradient.addColorStop(0.3, '#ff88ff');
            gradient.addColorStop(0.7, '#ff00ff');
            gradient.addColorStop(1, '#880088');
            ctx.fillStyle = gradient;
            ctx.fill();

            // 绘制边框
            ctx.strokeStyle = '#ff00ff';
            ctx.lineWidth = 2;
            ctx.stroke();
        }

        // 重置阴影
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
    }

    /**
     * 渲染整个游戏画面
     * @param {GameBoard} gameBoard - 游戏棋盘对象
     */
    render(gameBoard) {
        // 清空画布
        this.clear();
        
        // 绘制棋盘
        this.drawBoard();
        
        // 绘制所有棋子
        for (let row = 0; row < gameBoard.getSize(); row++) {
            for (let col = 0; col < gameBoard.getSize(); col++) {
                const piece = gameBoard.getPiece(row, col);
                if (piece !== 0) {
                    this.drawPiece(row, col, piece);
                }
            }
        }
    }

    /**
     * 将屏幕坐标转换为棋盘坐标
     * @param {number} x - 屏幕X坐标
     * @param {number} y - 屏幕Y坐标
     * @returns {Object|null} 棋盘坐标 {row, col} 或 null（如果无效）
     */
    screenToBoard(x, y) {
        // 获取Canvas的边界矩形
        const rect = this.canvas.getBoundingClientRect();
        
        // 转换为Canvas内部坐标
        const canvasX = x - rect.left;
        const canvasY = y - rect.top;
        
        // 转换为棋盘坐标
        const col = Math.round((canvasX - this.boardMargin) / this.cellSize);
        const row = Math.round((canvasY - this.boardMargin) / this.cellSize);
        
        // 检查坐标是否在有效范围内
        if (row >= 0 && row < this.boardSize && col >= 0 && col < this.boardSize) {
            return { row, col };
        }
        
        return null;
    }

    /**
     * 绘制最后落子的标记 - 赛博朋克风格
     * @param {number} row - 行坐标
     * @param {number} col - 列坐标
     */
    drawLastMoveMarker(row, col) {
        const ctx = this.ctx;
        const x = this.boardMargin + col * this.cellSize;
        const y = this.boardMargin + row * this.cellSize;

        // 绘制脉冲发光标记
        ctx.shadowColor = '#ffff00';
        ctx.shadowBlur = 10;

        // 外圈脉冲
        ctx.strokeStyle = '#ffff00';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.arc(x, y, this.pieceRadius + 8, 0, Math.PI * 2);
        ctx.stroke();

        // 内圈脉冲
        ctx.strokeStyle = 'rgba(255, 255, 0, 0.6)';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(x, y, this.pieceRadius + 5, 0, Math.PI * 2);
        ctx.stroke();

        // 重置阴影
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
    }
}
