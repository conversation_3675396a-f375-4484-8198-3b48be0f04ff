/**
 * WinChecker - 六子棋胜负判定模块
 * 负责检查游戏的胜负条件（六连珠）
 */
class WinChecker {
    constructor() {
        // 获胜需要的连子数量
        this.winLength = 6;
        
        // 四个检查方向：水平、垂直、对角线、反对角线
        this.directions = [
            [0, 1],   // 水平方向
            [1, 0],   // 垂直方向
            [1, 1],   // 主对角线方向
            [1, -1]   // 反对角线方向
        ];
    }

    /**
     * 检查指定位置是否获胜
     * @param {GameBoard} gameBoard - 游戏棋盘对象
     * @param {number} row - 最后落子的行坐标
     * @param {number} col - 最后落子的列坐标
     * @param {number} player - 玩家 (1=黑子, 2=白子)
     * @returns {Object|null} 获胜信息对象或null
     */
    checkWin(gameBoard, row, col, player) {
        // 检查四个方向
        for (let direction of this.directions) {
            const result = this.checkDirection(gameBoard, row, col, player, direction);
            if (result.isWin) {
                return {
                    isWin: true,
                    player: player,
                    direction: direction,
                    startPos: result.startPos,
                    endPos: result.endPos,
                    positions: result.positions
                };
            }
        }
        
        return null;
    }

    /**
     * 检查指定方向是否形成连珠
     * @param {GameBoard} gameBoard - 游戏棋盘对象
     * @param {number} row - 起始行坐标
     * @param {number} col - 起始列坐标
     * @param {number} player - 玩家
     * @param {Array} direction - 方向向量 [rowDelta, colDelta]
     * @returns {Object} 检查结果
     */
    checkDirection(gameBoard, row, col, player, direction) {
        const [rowDelta, colDelta] = direction;
        const positions = [];
        let count = 1; // 包含当前位置
        
        // 添加当前位置
        positions.push([row, col]);
        
        // 向正方向搜索
        let currentRow = row + rowDelta;
        let currentCol = col + colDelta;
        
        while (this.isValidPosition(gameBoard, currentRow, currentCol) &&
               gameBoard.getPiece(currentRow, currentCol) === player) {
            positions.push([currentRow, currentCol]);
            count++;
            currentRow += rowDelta;
            currentCol += colDelta;
        }
        
        // 向负方向搜索
        currentRow = row - rowDelta;
        currentCol = col - colDelta;
        
        while (this.isValidPosition(gameBoard, currentRow, currentCol) &&
               gameBoard.getPiece(currentRow, currentCol) === player) {
            positions.unshift([currentRow, currentCol]); // 添加到开头
            count++;
            currentRow -= rowDelta;
            currentCol -= colDelta;
        }
        
        // 检查是否达到获胜条件
        const isWin = count >= this.winLength;
        
        return {
            isWin: isWin,
            count: count,
            positions: positions,
            startPos: positions[0],
            endPos: positions[positions.length - 1]
        };
    }

    /**
     * 检查位置是否有效
     * @param {GameBoard} gameBoard - 游戏棋盘对象
     * @param {number} row - 行坐标
     * @param {number} col - 列坐标
     * @returns {boolean} 位置是否有效
     */
    isValidPosition(gameBoard, row, col) {
        const size = gameBoard.getSize();
        return row >= 0 && row < size && col >= 0 && col < size;
    }

    /**
     * 检查游戏是否平局（棋盘已满且无人获胜）
     * @param {GameBoard} gameBoard - 游戏棋盘对象
     * @returns {boolean} 是否平局
     */
    checkDraw(gameBoard) {
        return gameBoard.isFull();
    }

    /**
     * 获取所有可能的获胜线路（用于AI分析）
     * @param {GameBoard} gameBoard - 游戏棋盘对象
     * @param {number} player - 玩家
     * @returns {Array} 所有可能的获胜线路
     */
    getAllWinningLines(gameBoard, player) {
        const lines = [];
        const size = gameBoard.getSize();
        
        // 遍历棋盘上的每个位置
        for (let row = 0; row < size; row++) {
            for (let col = 0; col < size; col++) {
                // 检查四个方向
                for (let direction of this.directions) {
                    const line = this.getLineFromPosition(gameBoard, row, col, direction);
                    if (line.length >= this.winLength) {
                        lines.push(line);
                    }
                }
            }
        }
        
        return lines;
    }

    /**
     * 从指定位置和方向获取一条线路
     * @param {GameBoard} gameBoard - 游戏棋盘对象
     * @param {number} startRow - 起始行
     * @param {number} startCol - 起始列
     * @param {Array} direction - 方向向量
     * @returns {Array} 线路上的位置数组
     */
    getLineFromPosition(gameBoard, startRow, startCol, direction) {
        const [rowDelta, colDelta] = direction;
        const line = [];
        
        let currentRow = startRow;
        let currentCol = startCol;
        
        // 沿着方向收集位置
        while (this.isValidPosition(gameBoard, currentRow, currentCol)) {
            line.push([currentRow, currentCol]);
            currentRow += rowDelta;
            currentCol += colDelta;
        }
        
        return line;
    }

    /**
     * 分析指定位置的威胁程度（用于AI）
     * @param {GameBoard} gameBoard - 游戏棋盘对象
     * @param {number} row - 行坐标
     * @param {number} col - 列坐标
     * @param {number} player - 玩家
     * @returns {number} 威胁程度分数
     */
    analyzeThreat(gameBoard, row, col, player) {
        let maxThreat = 0;
        
        // 检查四个方向
        for (let direction of this.directions) {
            const result = this.checkDirection(gameBoard, row, col, player, direction);
            maxThreat = Math.max(maxThreat, result.count);
        }
        
        return maxThreat;
    }

    /**
     * 设置获胜所需的连子数量
     * @param {number} length - 连子数量
     */
    setWinLength(length) {
        if (length > 0) {
            this.winLength = length;
        }
    }

    /**
     * 获取当前的获胜连子数量
     * @returns {number} 获胜连子数量
     */
    getWinLength() {
        return this.winLength;
    }
}
